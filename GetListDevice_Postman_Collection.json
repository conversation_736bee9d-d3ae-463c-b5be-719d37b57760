{"info": {"_postman_id": "m2m-api-complete-collection", "name": "M2M API Complete Collection", "description": "Complete Postman collection for M2M APIs including GetListDevice test cases (TC06-TC31) and all other APIs from the specification", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "GetListDevice - Test Case 6 (TC06)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response contains devices from VN country\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.errorCode).to.eql(0);", "    pm.expect(jsonData.errorDesc).to.eql(\"SUCCESS\");", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/msimapi/getListDevice?country=VN", "host": ["{{base_url}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getListDevice"], "query": [{"key": "country", "value": "VN"}]}}, "response": []}, {"name": "GetListDevice - Test Case 7 (TC07)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response contains only smart_meter devices\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.errorCode).to.eql(0);", "    pm.expect(jsonData.errorDesc).to.eql(\"SUCCESS\");", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/msimapi/getListDevice?deviceType=smart_meter", "host": ["{{base_url}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getListDevice"], "query": [{"key": "deviceType", "value": "smart_meter"}]}}, "response": []}, {"name": "GetListDevice - Test Case 8 (TC08)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response contains devices within date range\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.errorCode).to.eql(0);", "    pm.expect(jsonData.errorDesc).to.eql(\"SUCCESS\");", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/msimapi/getListDevice?contractDateFrom=2024-01-01&contractDateTo=2024-12-31", "host": ["{{base_url}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getListDevice"], "query": [{"key": "contractDateFrom", "value": "2024-01-01"}, {"key": "contractDateTo", "value": "2024-12-31"}]}}, "response": []}, {"name": "GetListDevice - Test Case 9 (TC09)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response contains devices matching multiple filters\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.errorCode).to.eql(0);", "    pm.expect(jsonData.errorDesc).to.eql(\"SUCCESS\");", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/msimapi/getListDevice?msisdn=0988123456&deviceType=modem&country=VN", "host": ["{{base_url}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getListDevice"], "query": [{"key": "msisdn", "value": "0988123456"}, {"key": "deviceType", "value": "modem"}, {"key": "country", "value": "VN"}]}}, "response": []}, {"name": "GetListDevice - Test Case 10 (TC10)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response contains paginated results\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.errorCode).to.eql(0);", "    pm.expect(jsonData.data).to.have.length.at.most(10);", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/msimapi/getListDevice?page=1&size=10", "host": ["{{base_url}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getListDevice"], "query": [{"key": "page", "value": "1"}, {"key": "size", "value": "10"}]}}, "response": []}, {"name": "GetListDevice - Test Case 11 (TC11)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response contains sorted results (ascending)\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.errorCode).to.eql(0);", "    pm.expect(jsonData.errorDesc).to.eql(\"SUCCESS\");", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/msimapi/getListDevice?sort=contractDate,asc", "host": ["{{base_url}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getListDevice"], "query": [{"key": "sort", "value": "contractDate,asc"}]}}, "response": []}, {"name": "GetListDevice - Test Case 12 (TC12)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response contains sorted results (descending)\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.errorCode).to.eql(0);", "    pm.expect(jsonData.errorDesc).to.eql(\"SUCCESS\");", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/msimapi/getListDevice?sort=contractDate,desc", "host": ["{{base_url}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getListDevice"], "query": [{"key": "sort", "value": "contractDate,desc"}]}}, "response": []}, {"name": "GetListDevice - Test Case 13 (TC13)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 401\", function () {", "    pm.response.to.have.status(401);", "});", "", "pm.test(\"Response indicates unauthorized access\", function () {", "    pm.expect(pm.response.code).to.eql(401);", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/msimapi/getListDevice", "host": ["{{base_url}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getListDevice"]}}, "response": []}, {"name": "GetListDevice - Test Case 14 (TC14)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 402\", function () {", "    pm.response.to.have.status(402);", "});", "", "pm.test(\"Response indicates invalid token\", function () {", "    pm.expect(pm.response.code).to.eql(402);", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer invalid_token_here", "type": "text"}], "url": {"raw": "{{base_url}}/api/msimapi/getListDevice", "host": ["{{base_url}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getListDevice"]}}, "response": []}, {"name": "GetListDevice - Test Case 15 (TC15)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 402\", function () {", "    pm.response.to.have.status(402);", "});", "", "pm.test(\"Response indicates old token after register\", function () {", "    pm.expect(pm.response.code).to.eql(402);", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{old_token_after_register}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/msimapi/getListDevice", "host": ["{{base_url}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getListDevice"]}}, "response": []}, {"name": "GetListDevice - Test Case 16 (TC16)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 402\", function () {", "    pm.response.to.have.status(402);", "});", "", "pm.test(\"Response indicates old token after secret key change\", function () {", "    pm.expect(pm.response.code).to.eql(402);", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{old_token_after_secret_change}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/msimapi/getListDevice", "host": ["{{base_url}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getListDevice"]}}, "response": []}, {"name": "GetListDevice - Test Case 17 (TC17)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 402\", function () {", "    pm.response.to.have.status(402);", "});", "", "pm.test(\"Response indicates API permission deactivated\", function () {", "    pm.expect(pm.response.code).to.eql(402);", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{deactivated_api_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/msimapi/getListDevice", "host": ["{{base_url}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getListDevice"]}}, "response": []}, {"name": "GetListDevice - Test Case 18 (TC18)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 402\", function () {", "    pm.response.to.have.status(402);", "});", "", "pm.test(\"Response indicates inactive customer account\", function () {", "    pm.expect(pm.response.code).to.eql(402);", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{inactive_customer_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/msimapi/getListDevice", "host": ["{{base_url}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getListDevice"]}}, "response": []}, {"name": "GetListDevice - Test Case 19 (TC19)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response indicates error for unauthorized msisdn\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.errorCode).to.not.eql(0);", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/msimapi/getListDevice?msisdn=unauthorized_msisdn", "host": ["{{base_url}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getListDevice"], "query": [{"key": "msisdn", "value": "unauthorized_msisdn"}]}}, "response": []}, {"name": "GetListDevice - Test Case 20 (TC20)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response indicates error for non-existent msisdn\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.errorCode).to.not.eql(0);", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/msimapi/getListDevice?msisdn=9999999999", "host": ["{{base_url}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getListDevice"], "query": [{"key": "msisdn", "value": "9999999999"}]}}, "response": []}, {"name": "GetListDevice - Test Case 21 (TC21)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response indicates error for invalid msisdn format\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.errorCode).to.not.eql(0);", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/msimapi/getListDevice?msisdn=abc123", "host": ["{{base_url}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getListDevice"], "query": [{"key": "msisdn", "value": "abc123"}]}}, "response": []}, {"name": "GetListDevice - Test Case 22 (TC22)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response indicates error for too long imei\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.errorCode).to.not.eql(0);", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/msimapi/getListDevice?imei=12345678901234567890", "host": ["{{base_url}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getListDevice"], "query": [{"key": "imei", "value": "12345678901234567890"}]}}, "response": []}, {"name": "GetListDevice - Test Case 23 (TC23)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response indicates error for non-existent imei\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.errorCode).to.not.eql(0);", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/msimapi/getListDevice?imei=999999999999999", "host": ["{{base_url}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getListDevice"], "query": [{"key": "imei", "value": "999999999999999"}]}}, "response": []}, {"name": "GetListDevice - Test Case 24 (TC24)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response indicates error for special characters in location\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.errorCode).to.not.eql(0);", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/msimapi/getListDevice?location=@@@", "host": ["{{base_url}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getListDevice"], "query": [{"key": "location", "value": "@@@"}]}}, "response": []}, {"name": "GetListDevice - Test Case 25 (TC25)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response indicates error for non-existent country\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.errorCode).to.not.eql(0);", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/msimapi/getListDevice?country=ZZ", "host": ["{{base_url}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getListDevice"], "query": [{"key": "country", "value": "ZZ"}]}}, "response": []}, {"name": "GetListDevice - Test Case 26 (TC26)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response indicates error for invalid device type\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.errorCode).to.not.eql(0);", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/msimapi/getListDevice?deviceType=unknown_type", "host": ["{{base_url}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getListDevice"], "query": [{"key": "deviceType", "value": "unknown_type"}]}}, "response": []}, {"name": "GetListDevice - Test Case 27 (TC27)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response indicates error for invalid date range\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.errorCode).to.not.eql(0);", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/msimapi/getListDevice?contractDateFrom=2024-12-31&contractDateTo=2024-01-01", "host": ["{{base_url}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getListDevice"], "query": [{"key": "contractDateFrom", "value": "2024-12-31"}, {"key": "contractDateTo", "value": "2024-01-01"}]}}, "response": []}, {"name": "GetListDevice - Test Case 28 (TC28)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response indicates error for invalid date format\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.errorCode).to.not.eql(0);", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/msimapi/getListDevice?contractDateFrom=2024/01/01", "host": ["{{base_url}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getListDevice"], "query": [{"key": "contractDateFrom", "value": "2024/01/01"}]}}, "response": []}, {"name": "GetListDevice - Test Case 29 (TC29)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response indicates error for negative page\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.errorCode).to.not.eql(0);", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/msimapi/getListDevice?page=-1", "host": ["{{base_url}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getListDevice"], "query": [{"key": "page", "value": "-1"}]}}, "response": []}, {"name": "GetListDevice - Test Case 30 (TC30)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response indicates error for too large size\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.errorCode).to.not.eql(0);", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/msimapi/getListDevice?size=1000", "host": ["{{base_url}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getListDevice"], "query": [{"key": "size", "value": "1000"}]}}, "response": []}, {"name": "GetListDevice - Test Case 31 (TC31)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response indicates error for invalid sort format\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.errorCode).to.not.eql(0);", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/msimapi/getListDevice?sort=contractDate-down", "host": ["{{base_url}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getListDevice"], "query": [{"key": "sort", "value": "contractDate-down"}]}}, "response": []}, {"name": "GetDetailDevice - API #7", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response contains device details\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.errorCode).to.eql(0);", "    pm.expect(jsonData.errorDesc).to.eql(\"SUCCESS\");", "    pm.expect(jsonData.data).to.be.an('object');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/msimapi/getDetailDevice?msisdn=84823384832", "host": ["{{base_url}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getDetailDevice"], "query": [{"key": "msisdn", "value": "84823384832"}]}}, "response": []}, {"name": "CreateDevice - API #8", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Devi<PERSON> created successfully\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.errorCode).to.eql(0);", "    pm.expect(jsonData.errorDesc).to.eql(\"SUCCESS\");", "    pm.expect(jsonData.data).to.be.an('object');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"entity\": {\n    \"msisdn\": \"84823384833\",\n    \"imei\": \"123456789012346\",\n    \"location\": \"Hanoi\",\n    \"country\": \"Vietnam\",\n    \"deviceType\": \"IoT Device\"\n  }\n}"}, "url": {"raw": "{{base_url}}/api/msimapi/createDevice", "host": ["{{base_url}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "createDevice"]}}, "response": []}, {"name": "UpdateDevice - API #9", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"<PERSON><PERSON> updated successfully\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.errorCode).to.eql(0);", "    pm.expect(jsonData.errorDesc).to.eql(\"SUCCESS\");", "    pm.expect(jsonData.data).to.be.an('object');", "});"], "type": "text/javascript"}}], "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"entity\": {\n    \"msisdn\": \"84823384832\",\n    \"imei\": \"123456789012345\",\n    \"location\": \"Ho Chi Minh City\",\n    \"country\": \"Vietnam\",\n    \"deviceType\": \"Smart Meter\"\n  }\n}"}, "url": {"raw": "{{base_url}}/api/msimapi/updateDevice", "host": ["{{base_url}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "updateDevice"]}}, "response": []}, {"name": "GetListCustomer - API #10", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response contains customer list\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.errorCode).to.eql(0);", "    pm.expect(jsonData.errorDesc).to.eql(\"SUCCESS\");", "    pm.expect(jsonData.data).to.be.an('array');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/msimapi/getListCustomer?page=0&size=10", "host": ["{{base_url}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getListCustomer"], "query": [{"key": "page", "value": "0"}, {"key": "size", "value": "10"}]}}, "response": []}, {"name": "GetDetailCustomer - API #11", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response contains customer details\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.errorCode).to.eql(0);", "    pm.expect(jsonData.errorDesc).to.eql(\"SUCCESS\");", "    pm.expect(jsonData.data).to.be.an('object');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/msimapi/getDetailCustomer/1", "host": ["{{base_url}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getDetailCustomer", "1"]}}, "response": []}, {"name": "GetListContract - API #12", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response contains contract list\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.errorCode).to.eql(0);", "    pm.expect(jsonData.errorDesc).to.eql(\"SUCCESS\");", "    pm.expect(jsonData.data).to.be.an('array');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/msimapi/getListContract?page=0&size=10", "host": ["{{base_url}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getListContract"], "query": [{"key": "page", "value": "0"}, {"key": "size", "value": "10"}]}}, "response": []}, {"name": "GetDetailContract - API #13", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response contains contract details\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.errorCode).to.eql(0);", "    pm.expect(jsonData.errorDesc).to.eql(\"SUCCESS\");", "    pm.expect(jsonData.data).to.be.an('object');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/msimapi/getDetailContract?contractCode=HD001", "host": ["{{base_url}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getDetailContract"], "query": [{"key": "contractCode", "value": "HD001"}]}}, "response": []}, {"name": "GetListTrafficWallet - API #14", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response contains traffic wallet list\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.errorCode).to.eql(0);", "    pm.expect(jsonData.errorDesc).to.eql(\"SUCCESS\");", "    pm.expect(jsonData.data).to.be.an('array');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"trafficWalletReqDTO\": {\n    \"page\": 0,\n    \"size\": 10\n  }\n}"}, "url": {"raw": "{{base_url}}/api/msimapi/getListTrafficWallet", "host": ["{{base_url}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getListTrafficWallet"]}}, "response": []}, {"name": "GetDetailTrafficWallet - API #15", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response contains traffic wallet details\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.errorCode).to.eql(0);", "    pm.expect(jsonData.errorDesc).to.eql(\"SUCCESS\");", "    pm.expect(jsonData.data).to.be.an('object');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/msimapi/getDetailTrafficWallet?paymentCode=PAY001&fromDate=2024-01-01&toDate=2024-01-31", "host": ["{{base_url}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getDetailTrafficWallet"], "query": [{"key": "paymentCode", "value": "PAY001"}, {"key": "fromDate", "value": "2024-01-01"}, {"key": "toDate", "value": "2024-01-31"}]}}, "response": []}, {"name": "GetListShareWallet - API #16", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response contains share wallet list\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.errorCode).to.eql(0);", "    pm.expect(jsonData.errorDesc).to.eql(\"SUCCESS\");", "    pm.expect(jsonData.data).to.be.an('array');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/msimapi/getListShareWallet?subCode=SUB001&page=0&size=10", "host": ["{{base_url}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getListShareWallet"], "query": [{"key": "subCode", "value": "SUB001"}, {"key": "page", "value": "0"}, {"key": "size", "value": "10"}]}}, "response": []}, {"name": "GetWalletHistory - API #17", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response contains wallet history\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.errorCode).to.eql(0);", "    pm.expect(jsonData.errorDesc).to.eql(\"SUCCESS\");", "    pm.expect(jsonData.data).to.be.an('array');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/msimapi/getWalletHistory?page=0&size=10", "host": ["{{base_url}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getWalletHistory"], "query": [{"key": "page", "value": "0"}, {"key": "size", "value": "10"}]}}, "response": []}, {"name": "ShareTraffic - API #18", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Traffic shared successfully\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.errorCode).to.eql(0);", "    pm.expect(jsonData.errorDesc).to.eql(\"SUCCESS\");", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"trafficShareReqDTO\": {\n    \"fromMsisdn\": \"84823384832\",\n    \"toMsisdn\": \"84823384833\",\n    \"amount\": 1024000,\n    \"unit\": \"bytes\"\n  }\n}"}, "url": {"raw": "{{base_url}}/api/msimapi/shareTraffic", "host": ["{{base_url}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "shareTraffic"]}}, "response": []}, {"name": "GetTerminalUsageDataDetails - API #19", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response contains usage data details\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.errorCode).to.eql(0);", "    pm.expect(jsonData.errorDesc).to.eql(\"SUCCESS\");", "    pm.expect(jsonData.data).to.be.an('array');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/msimapi/getTerminalUsageDataDetails?msisdn=84823384832&fromDate=2024-01-01&toDate=2024-01-31", "host": ["{{base_url}}"], "path": ["api", "m<PERSON><PERSON><PERSON>", "getTerminalUsageDataDetails"], "query": [{"key": "msisdn", "value": "84823384832"}, {"key": "fromDate", "value": "2024-01-01"}, {"key": "toDate", "value": "2024-01-31"}]}}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "base_url", "value": "https://api-m2m.vinaphone.com.vn", "type": "string"}, {"key": "auth_token", "value": "your_bearer_token_here", "type": "string"}, {"key": "old_token_after_register", "value": "old_token_after_register_here", "type": "string"}, {"key": "old_token_after_secret_change", "value": "old_token_after_secret_change_here", "type": "string"}, {"key": "deactivated_api_token", "value": "deactivated_api_token_here", "type": "string"}, {"key": "inactive_customer_token", "value": "inactive_customer_token_here", "type": "string"}]}